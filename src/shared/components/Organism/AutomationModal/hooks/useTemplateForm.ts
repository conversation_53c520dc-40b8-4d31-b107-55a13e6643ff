import { useState, useCallback, useMemo } from 'react';
import type {
  TemplateFormData,
  NormalizedTemplate,
  UseTemplateFormOptions,
  TemplateFormConfig,
} from '../types/template.types';

const createEmptyTemplate = (): TemplateFormData => ({
  templateName: '',
  subject: '',
  message: '',
  delay: 'IMMEDIATELY',
  attachments: [],
  hasFollowup: false,
  followupPeriod: '',
  followupTitle: '',
  followupMessage: '',
  followupAttachments: [],
});

const transformTemplateToFormData = (
  template: NormalizedTemplate
): TemplateFormData => ({
  templateName: template.title || '',
  subject: template.subject || '',
  message: template.message || '',
  delay: 'IMMEDIATELY',
  attachments: (template.fileIds || []).map((id: string | number) => ({ id })),
  hasFollowup: template.hasFollowup || false,
  followupPeriod: template.followupPeriod || '',
  followupTitle: template.followupTitle || '',
  followupMessage: template.followupMessage || '',
  followupAttachments: template.followupAttachments || [],
});

export const useTemplateForm = (options: UseTemplateFormOptions = {}) => {
  const { initialData, onFormChange, onSubmit, config = {} } = options;

  const [isFormValid, setIsFormValid] = useState(false);
  const [currentFormValues, setCurrentFormValues] = useState<TemplateFormData>(
    initialData || createEmptyTemplate()
  );
  const [editingTemplate, setEditingTemplate] =
    useState<NormalizedTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const formConfig: Required<TemplateFormConfig> = {
    showDelay: true,
    showFollowup: true,
    showAttachments: true,
    readOnly: false,
    customFields: [],
    ...config,
  };

  const initialFormData = useMemo(() => {
    if (editingTemplate) {
      return transformTemplateToFormData(editingTemplate);
    }

    return initialData || createEmptyTemplate();
  }, [editingTemplate, initialData]);

  const handleFormChange = useCallback(
    (values: TemplateFormData, isValid: boolean) => {
      setCurrentFormValues(values);
      setIsFormValid(isValid);
      onFormChange?.(values, isValid);
    },
    [onFormChange]
  );

  const handleSubmit = useCallback(
    async (data?: TemplateFormData) => {
      const formData = data || currentFormValues;
      const isCreate = !editingTemplate;

      try {
        setIsLoading(true);
        await onSubmit?.(formData, isCreate);
      } catch (error) {
        console.error('Error submitting form:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [currentFormValues, editingTemplate, onSubmit]
  );

  const startEditing = useCallback((template: NormalizedTemplate) => {
    setEditingTemplate(template);
    const formData = transformTemplateToFormData(template);
    setCurrentFormValues(formData);
    setIsFormValid(true);
  }, []);

  const startCreating = useCallback(() => {
    setEditingTemplate(null);
    const emptyData = createEmptyTemplate();
    setCurrentFormValues(emptyData);
    setIsFormValid(false);
  }, []);

  const resetForm = useCallback(() => {
    setEditingTemplate(null);
    setCurrentFormValues(createEmptyTemplate());
    setIsFormValid(false);
    setIsLoading(false);
  }, []);

  const loadTemplate = useCallback(
    async (template: NormalizedTemplate) => {
      try {
        setIsLoading(true);
        startEditing(template);
      } catch (error) {
        console.error('Error loading template:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [startEditing]
  );

  const isEditing = editingTemplate !== null;
  const canSubmit = isFormValid && !isLoading;

  return {
    formData: initialFormData,
    currentFormValues,
    editingTemplate,
    isFormValid,
    isLoading,
    isEditing,
    canSubmit,
    config: formConfig,
    handleFormChange,
    handleSubmit,
    startEditing,
    startCreating,
    resetForm,
    loadTemplate,
    createEmptyTemplate,
    transformTemplateToFormData,
  };
};
