import { useState } from 'react';
import { useTemplateList } from '@shared/components/Organism/AutomationModal/hooks/useTemplateList';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import {
  createEmailTemplate,
  updateEmailTemplate,
  setEmailTemplateDefault,
  removeEmailTemplateDefault,
  type CreateEmailTemplateRequest,
} from '@shared/utils/api/template';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type {
  TemplateFormData,
  NormalizedTemplate,
  UseTemplateActionsOptions,
} from '../types/template.types';

export const useTemplateActions = (options: UseTemplateActionsOptions = {}) => {
  const {
    onTemplateCreated,
    onTemplateUpdated,
    onTemplateDeleted,
    onDefaultChanged,
    confirmDelete = true,
  } = options;

  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpdatingDefault, setIsUpdatingDefault] = useState<string | null>(
    null
  );

  const templateList = useTemplateList({
    searchEnabled: true,
  });

  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });

  const { mutate: removeEmailTemplate } = useReactMutation({
    apiFunc: removeEmailTemplateDefault,
    onSuccess: () => {
      templateList.refetch();
    },
  });

  const transformFormDataToApi = (
    data: TemplateFormData
  ): CreateEmailTemplateRequest => ({
    title: data.templateName,
    subject: data.subject,
    message: data.message,
    timeDelay: data.delay === 'IMMEDIATELY' ? 'IMMEDIATELY' : data.delay,
    fileIds: (data.attachments || []).map((attachment: any) =>
      typeof attachment === 'number'
        ? attachment
        : parseInt(attachment.id || attachment, 10)
    ),
    hasFollowup: data.hasFollowup,
    ...(data.hasFollowup && {
      followupTitle: data.followupTitle,
      followupMessage: data.followupMessage,
      followupPeriod: data.followupPeriod,
      followupFileIds: (data.followupAttachments || []).map(
        (attachment: any) =>
          typeof attachment === 'number'
            ? attachment
            : parseInt(attachment.id || attachment, 10)
      ),
    }),
  });

  const createTemplate = async (
    data: TemplateFormData
  ): Promise<NormalizedTemplate | null> => {
    try {
      setIsSubmitting(true);
      const apiData = transformFormDataToApi(data);
      const result = await createEmailTemplate(apiData);

      const normalizedTemplate: NormalizedTemplate = {
        id: result.id.toString(),
        title: result.title || '',
        subject: result.subject || '',
        message: result.message || '',
        fileIds: (result.fileIds || []).map((id) => id.toString()),
        hasFollowup: result.hasFollowup || false,
        default: result.default || false,
      };

      onTemplateCreated?.(normalizedTemplate);

      return normalizedTemplate;
    } catch (error) {
      console.error('Error creating template:', error);

      return null;
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateTemplate = async (
    templateId: number,
    data: TemplateFormData
  ): Promise<NormalizedTemplate | null> => {
    try {
      setIsSubmitting(true);
      const apiData = transformFormDataToApi(data);
      const result = await updateEmailTemplate(templateId, apiData);

      const normalizedTemplate: NormalizedTemplate = {
        id: result.id.toString(),
        title: result.title || '',
        subject: result.subject || '',
        message: result.message || '',
        fileIds: (result.fileIds || []).map((id) => id.toString()),
        hasFollowup: result.hasFollowup || false,
        default: result.default || false,
      };

      onTemplateUpdated?.(normalizedTemplate);

      return normalizedTemplate;
    } catch (error) {
      console.error('Error updating template:', error);

      return null;
    } finally {
      setIsSubmitting(false);
    }
  };

  const deleteTemplate = async (
    template: NormalizedTemplate
  ): Promise<boolean> => {
    const performDelete = async () => {
      try {
        removeEmailTemplate(template.id);
        onTemplateDeleted?.(template.id);

        return true;
      } catch (error) {
        return false;
      }
    };

    if (confirmDelete) {
      openConfirmDialog({
        title: t('delete_template'),
        message: t('delete_template_confirm'),
        confirmButtonText: t('delete'),
        cancelButtonText: t('cancel'),
        isAjaxCall: true,
        apiProps: {
          func: performDelete,
        },
      });

      return true;
    }

    return performDelete();
  };

  const setDefaultTemplate = async (
    templateId: string,
    isCurrentlyDefault: boolean
  ): Promise<boolean> => {
    if (isUpdatingDefault === templateId) {
      return false;
    }

    try {
      setIsUpdatingDefault(templateId);
      const id = parseInt(templateId, 10);

      if (isCurrentlyDefault) {
        await removeEmailTemplateDefault(id);
      } else {
        await setEmailTemplateDefault(id);
      }

      onDefaultChanged?.(templateId, !isCurrentlyDefault);

      return true;
    } catch (error) {
      console.error('Error updating default template:', error);

      return false;
    } finally {
      setIsUpdatingDefault(null);
    }
  };

  const duplicateTemplate = async (
    template: NormalizedTemplate
  ): Promise<NormalizedTemplate | null> => {
    const duplicateData: TemplateFormData = {
      templateName: `${template.title} (Copy)`,
      subject: template.subject,
      message: template.message,
      delay: 'IMMEDIATELY',
      attachments: template.fileIds.map((id) => ({ id })),
      hasFollowup: template.hasFollowup,
      followupPeriod: template.followupPeriod || '',
      followupTitle: template.followupTitle || '',
      followupMessage: template.followupMessage || '',
      followupAttachments: template.followupAttachments || [],
    };

    return createTemplate(duplicateData);
  };

  return {
    // Actions
    createTemplate,
    updateTemplate,
    deleteTemplate,
    setDefaultTemplate,
    duplicateTemplate,
    isSubmitting,
    isUpdatingDefault,
    transformFormDataToApi,
  };
};
