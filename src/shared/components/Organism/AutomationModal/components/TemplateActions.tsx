import React from 'react';
import IconButton from '@shared/uikit/Button/IconButton';
import MenuItem from '@shared/uikit/MenuItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import type { TemplateActionsProps } from '../types/template.types';

const TemplateActions: React.FC<TemplateActionsProps> = ({
  template,
  actions,
  isOpen,
  onOpen,
  onClose,
}) => {
  if (!actions || actions.length === 0) {
    return null;
  }

  return (
    <PopperMenu
      placement="bottom-end"
      onOpen={onOpen}
      onClose={onClose}
      closeOnScroll
      buttonComponent={
        <IconButton
          type="far"
          name="ellipsis-h"
          size="md"
          noHover
          colorSchema="transparent"
        />
      }
    >
      {actions.map((action) => (
        <MenuItem
          key={action.id}
          iconName={action.icon}
          title={action.title}
          onClick={() => action.onClick(template)}
          iconType={action.iconType || 'far'}
        />
      ))}
    </PopperMenu>
  );
};

export default TemplateActions;
