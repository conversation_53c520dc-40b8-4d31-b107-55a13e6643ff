import React, { useState } from 'react';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import Flex from '@shared/uikit/Flex';
import Button from '@shared/uikit/Button';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Typography from '@shared/uikit/Typography';
import MenuItem from '@shared/uikit/MenuItem';
import Skeleton from '@shared/uikit/Skeleton';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import {
  searchRejectionTemplates,
  getRejectionTemplateById,
  updateRejectionTemplate,
  deleteRejectionTemplate,
  setDefaultRejectionTemplate,
  removeDefaultRejectionTemplate,
} from '@shared/utils/api/template';
import { postReject } from '@shared/utils/api/jobs';

import TemplateForm from './TemplateForm';

const emptyForm = {
  templateName: '',
  subject: '',
  message: '',
  delay: 'Immediately',
  attachments: [],
};

const RejectionModal: React.FC = () => {
  const { t } = useTranslation();
  const automationState = useMultiStepFormState('automation');
  const isOpen = automationState?.isOpen || false;
  const isApplicant = (automationState?.data as any)?.isApplicant;
  const participationId = (automationState?.data as any)?.participationId;

  const [searchQuery, setSearchQuery] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);
  const [isUpdatingDefault, setIsUpdatingDefault] = useState<string | null>(
    null
  );
  const [currentDefaultTemplateId, setCurrentDefaultTemplateId] = useState<
    string | null
  >(null);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(
    null
  );
  const [templates, setTemplates] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);

  const fetchTemplates = async (query = '') => {
    setIsLoading(true);
    try {
      const res = await searchRejectionTemplates({
        text: query,
        page: 0,
        size: 10,
      });
      setTemplates(res?.content || []);
      // Set default template id if any
      const defaultTemplate = (res?.content || []).find(
        (tpl: any) => tpl.default
      );
      setCurrentDefaultTemplateId(
        defaultTemplate ? String(defaultTemplate.id) : null
      );
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    if (isOpen) fetchTemplates();
  }, [isOpen]);

  const handleClose = () => {
    closeMultiStepForm('automation');
    // Note: The original logic for opening candidate manager should be handled
    // by the component that originally opened the rejection modal
  };

  const handleSetDefault = async (
    templateId: string,
    isCurrentlyDefault: boolean,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();
    if (isUpdatingDefault === templateId) return;
    try {
      setIsUpdatingDefault(templateId);
      if (isCurrentlyDefault) {
        await removeDefaultRejectionTemplate(templateId);
        setCurrentDefaultTemplateId(null);
      } else {
        await setDefaultRejectionTemplate(templateId);
        setCurrentDefaultTemplateId(templateId);
      }
      fetchTemplates(searchQuery);
    } finally {
      setIsUpdatingDefault(null);
    }
  };

  const handleCreate = () => {
    setEditingTemplateId(null);
    setShowForm(true);
    setSelectedTemplate(null);
  };

  const handleTemplateClick = async (templateId: string) => {
    setIsLoadingTemplate(true);
    try {
      const tpl = await getRejectionTemplateById(templateId);
      setEditingTemplateId(String(tpl.id));
      setShowForm(false);
      setSelectedTemplate(tpl);
    } finally {
      setIsLoadingTemplate(false);
    }
  };

  const handleEditTemplate = async (templateId: string) => {
    setIsLoadingTemplate(true);
    try {
      const tpl = await getRejectionTemplateById(templateId);
      setEditingTemplateId(String(tpl.id));
      setShowForm(true);
      setSelectedTemplate(null);
    } finally {
      setIsLoadingTemplate(false);
    }
  };

  const handleFormDiscard = () => {
    setShowForm(false);
    setEditingTemplateId(null);
    setIsFormValid(false);
    setSelectedTemplate(null);
  };

  const handleFormChange = (_values: any, isValid: boolean) => {
    setIsFormValid(isValid);
  };

  const handleFormUpdate = async (data: any) => {
    setIsSubmitting(true);
    try {
      const body = {
        title: data.title,
        subject: data.subject,
        message: data.message,
        fileIds: (data.attachments || []).map((a: any) => Number(a.id)),
      };
      await updateRejectionTemplate(editingTemplateId || '', body);
      setShowForm(false);
      setEditingTemplateId(null);
      setIsFormValid(false);
      setSelectedTemplate(null);
      fetchTemplates(searchQuery);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (templateId: string) => {
    setIsSubmitting(true);
    try {
      await deleteRejectionTemplate(templateId);
      fetchTemplates(searchQuery);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!selectedTemplate || !participationId) return;

    setIsSubmitting(true);
    try {
      await postReject({
        participationId: Number(participationId),
        templateId: Number(selectedTemplate.id),
        title: selectedTemplate.title,
        subject: selectedTemplate.subject,
        message: selectedTemplate.message,
        fileIds: selectedTemplate.fileIds || [],
        automated: true,
      });
      handleClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderTemplateSkeleton = () => (
    <Flex className="flex-1 gap-12">
      <Skeleton style={{ width: '60%', height: 20, borderRadius: 4 }} />
      <Flex className="gap-8">
        <Skeleton style={{ width: 60, height: 16, borderRadius: 4 }} />
        <Skeleton style={{ width: '200px', height: 16, borderRadius: 4 }} />
      </Flex>
    </Flex>
  );

  let initialFormData = emptyForm;
  if (showForm && editingTemplateId && templates.length > 0) {
    const template = templates.find((t) => String(t.id) === editingTemplateId);
    if (template) {
      initialFormData = {
        templateName: template.title || '',
        subject: template.subject || '',
        message: template.message || '',
        attachments: (template.fileIds || []).map((id: string | number) => ({
          id,
        })),
        delay: template.delay,
      };
    }
  } else if (showForm && !editingTemplateId) {
    initialFormData = emptyForm;
  }

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
      modalClassName="overflow-hidden"
      contentClassName="overflow-hidden"
      modalDialogClassName="overflow-hidden"
      isOpen={isOpen}
    >
      <ModalHeaderSimple
        title={
          showForm
            ? editingTemplateId
              ? t('edit_template')
              : t('create_template')
            : isApplicant
              ? t('Applicant rejection')
              : t('Candidate rejection')
        }
        backButtonProps={{
          onClick: showForm ? handleFormDiscard : handleClose,
        }}
        noCloseButton
        hideBack={false}
      />
      <ModalBody className="p-6 h-full overflow-auto mb-8">
        {showForm ? (
          <TemplateForm
            formData={initialFormData}
            onSubmit={handleFormUpdate}
            isLoading={isLoadingTemplate}
            onChange={handleFormChange}
            omitFollowup
            showAttachments={isApplicant}
          />
        ) : selectedTemplate ? (
          <Flex className="bg-gray_5 rounded-[12px] p-12  gap-4">
            <Flex
              flexDir="row"
              className="w-full items-center mb-4 p-4 !justify-between"
            >
              <Flex className=" justify-center">
                <Typography className="font-semibold text-xl text-white">
                  {t('auto_reply')}
                </Typography>
              </Flex>
              <Button
                label={t('modify_template')}
                schema="semi-transparent"
                variant="default"
                onClick={() => handleEditTemplate(selectedTemplate.id)}
                className="mr-4"
              />
            </Flex>
            <TemplateForm
              formData={{
                templateName: selectedTemplate.title || '',
                subject: selectedTemplate.subject || '',
                message: selectedTemplate.message || '',
                delay: '',
                attachments: (selectedTemplate.fileIds || []).map(
                  (id: string | number) => ({ id })
                ),
              }}
              onSubmit={handleReject}
              isLoading={isLoadingTemplate}
              onChange={handleFormChange}
              omitFollowup
              readOnly
              showAttachments={isApplicant}
            />
          </Flex>
        ) : (
          <Flex className="flex-col h-full gap-8">
            <Flex className="flex-col gap-8 h-full">
              <SearchInputV2
                placeholder={t('search_templates')}
                onChange={(value) => {
                  setSearchQuery(value);
                  fetchTemplates(value);
                }}
                value={searchQuery}
              />
              <Flex className="flex-col gap-2 flex-1 overflow-y-auto">
                {isLoading ? (
                  Array.from({ length: 3 }).map((_, index) => (
                    <React.Fragment key={index}>
                      {renderTemplateSkeleton()}
                    </React.Fragment>
                  ))
                ) : templates.length === 0 ? (
                  <Typography>No templates found</Typography>
                ) : (
                  templates.map((template) => (
                    <MenuItem
                      key={template.id}
                      title={template.title}
                      actionClassName="justify-end"
                      subTitle={
                        <Typography className="max-w-[200px] flex flex-col mt-4 text-xs whitespace-nowrap overflow-hidden text-ellipsis">
                          <Typography className="text-gray-400">
                            Subject:
                          </Typography>
                          <Typography className="text-xs whitespace-nowrap overflow-hidden text-ellipsis max-w-[200px] inline-block">
                            {template.subject}
                          </Typography>
                        </Typography>
                      }
                      className="rounded-lg overflow-hidden flex justify-between items-center border border-gray-200 bg-gray-50 p-4 transition-all duration-200 ease-in-out hover:border-brand hover:bg-brand-50"
                      onClick={() => handleTemplateClick(template.id)}
                      actionElement={
                        <Flex className="flex-shrink-0 items-center">
                          <Button
                            schema={
                              currentDefaultTemplateId === String(template.id)
                                ? 'success-semi-transparent'
                                : 'ghost'
                            }
                            variant="default"
                            label={
                              currentDefaultTemplateId === String(template.id)
                                ? t('default')
                                : t('set_as_default')
                            }
                            leftIcon={
                              currentDefaultTemplateId === String(template.id)
                                ? 'check'
                                : undefined
                            }
                            leftType="fas"
                            labelColor={
                              currentDefaultTemplateId === String(template.id)
                                ? 'success'
                                : 'primaryText'
                            }
                            onClick={(e) => {
                              handleSetDefault(
                                String(template.id),
                                currentDefaultTemplateId ===
                                  String(template.id),
                                e
                              );
                            }}
                            className="min-w-[120px] whitespace-nowrap"
                            disabled={isUpdatingDefault === String(template.id)}
                            isLoading={
                              isUpdatingDefault === String(template.id)
                            }
                          />
                        </Flex>
                      }
                    />
                  ))
                )}
              </Flex>
            </Flex>
          </Flex>
        )}
      </ModalBody>
      <ModalFooter>
        {showForm ? (
          <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0 ">
            <Button
              label={t('discard')}
              schema="gray"
              variant="default"
              onClick={handleFormDiscard}
              className="flex-1"
            />
            <Button
              label={editingTemplateId ? t('update') : t('create')}
              schema="primary-blue"
              variant="default"
              onClick={() => handleFormUpdate(initialFormData)}
              className="flex-1"
              disabled={!isFormValid || isSubmitting || isLoadingTemplate}
              isLoading={isSubmitting}
            />
          </Flex>
        ) : selectedTemplate ? (
          <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0">
            <Button
              label={t('discard')}
              schema="gray"
              variant="default"
              onClick={handleFormDiscard}
              className="flex-1"
            />
            <Button
              label={t('reject')}
              schema="primary-blue"
              variant="default"
              onClick={() => handleReject()}
              className="flex-1"
              disabled={isSubmitting || isLoadingTemplate}
              isLoading={isSubmitting}
            />
          </Flex>
        ) : (
          <Button
            fullWidth
            label={t('create')}
            leftIcon="plus"
            leftType="fas"
            schema="ghost-brand"
            variant="default"
            onClick={handleCreate}
          />
        )}
      </ModalFooter>
    </FixedRightSideModal>
  );
};

export default RejectionModal;
