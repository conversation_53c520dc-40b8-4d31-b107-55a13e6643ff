import React, { useState } from 'react';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import MenuItem from '@shared/uikit/MenuItem';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import Skeleton from '@shared/uikit/Skeleton';
import Typography from '@shared/uikit/Typography';
import {
  createEmailTemplate,
  getEmailTemplateById,
  updateEmailTemplate,
  setEmailTemplateDefault,
  removeEmailTemplateDefault,
  type CreateEmailTemplateRequest,
} from '@shared/utils/api/template';
import useEmailTemplates from '@shared/utils/hooks/useEmailTemplates';
import useTranslation from '@shared/utils/hooks/useTranslation';
import TemplateForm from './TemplateForm';
import PopperMenu from '@shared/uikit/PopperMenu';
import IconButton from '@shared/uikit/Button/IconButton';
import Icon from '@shared/uikit/Icon';
import useDisclosure from '@shared/utils/hooks/useDisclosure';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import useReactMutation from '@shared/utils/hooks/useReactMutation';

interface TemplateFormData {
  templateName: string;
  subject: string;
  message: string;
  delay: string;
  attachments: any[];
  hasFollowup: boolean;
  followupPeriod: string;
  followupTitle: string;
  followupMessage: string;
  followupAttachments: any[];
}

const AutoReplyModal: React.FC = () => {
  const { t } = useTranslation();
  const automationState = useMultiStepFormState('automation');
  const [searchQuery, setSearchQuery] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);
  const [isUpdatingDefault, setIsUpdatingDefault] = useState<string | null>(
    null
  );
  const [currentDefaultTemplateId, setCurrentDefaultTemplateId] = useState<
    string | null
  >(null);
  const [editingTemplateId, setEditingTemplateId] = useState<number | null>(
    null
  );

  const { templates: apiTemplates, isLoading, refetch } = useEmailTemplates();
  const { isOpen, onClose, onOpen } = useDisclosure();

  const templates = apiTemplates?.map((template) => ({
    id: template.id.toString(),
    title: template.title || '',
    subject: template.subject || '',
    message: template.message || '',
    fileIds: (template.fileIds || []).map((id) => id.toString()),
    hasFollowup: template.hasFollowup || false,
    default: template.default || false,
  }));

  const filteredTemplates = templates.filter(
    (template) =>
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.subject.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const emptyTemplate: TemplateFormData = {
    templateName: '',
    subject: '',
    message: '',
    delay: '',
    attachments: [],
    hasFollowup: false,
    followupPeriod: '',
    followupTitle: '',
    followupMessage: '',
    followupAttachments: [],
  };

  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });

  const { mutate: removeEmailTemplate } = useReactMutation({
    apiFunc: removeEmailTemplateDefault,
    onSuccess: () => {
      refetch();
    },
  });

  const [isFormValid, setIsFormValid] = useState(false);
  const [currentFormValues, setCurrentFormValues] =
    useState<TemplateFormData>(emptyTemplate);

  let initialFormData = emptyTemplate;
  if (showForm && editingTemplateId && templates.length > 0) {
    const template = templates.find(
      (t) => t.id === editingTemplateId.toString()
    );
    if (template) {
      initialFormData = {
        templateName: template.title || '',
        subject: template.subject || '',
        message: template.message || '',
        delay: 'IMMEDIATELY',
        attachments: (template.fileIds || []).map((id: string | number) => ({
          id,
        })),
        hasFollowup: template.hasFollowup || false,
        followupPeriod: '',
        followupTitle: '',
        followupMessage: '',
        followupAttachments: [],
      };
    }
  }

  const handleFormChange = (values: TemplateFormData, isValid: boolean) => {
    setCurrentFormValues(values);
    setIsFormValid(isValid);
  };

  const handleClose = () => {
    closeMultiStepForm('automation');
    openMultiStepForm({
      formName: 'automation',
      data: automationState.data,
      type: 'main',
    });
  };

  const handleSetDefault = async (
    templateId: string,
    isCurrentlyDefault: boolean,
    e?: React.MouseEvent
  ) => {
    e?.stopPropagation();

    if (isUpdatingDefault === templateId) {
      return;
    }

    try {
      setIsUpdatingDefault(templateId);
      const id = parseInt(templateId, 10);

      if (isCurrentlyDefault) {
        await removeEmailTemplateDefault(id);
        setCurrentDefaultTemplateId(null);
      } else {
        await setEmailTemplateDefault(id);
        setCurrentDefaultTemplateId(templateId);
      }

      refetch();
    } catch (error) {
      console.error('Error updating default template:', error);
    } finally {
      setIsUpdatingDefault(null);
    }
  };

  const handleCreate = () => {
    setEditingTemplateId(null);
    setShowForm(true);
  };

  const handleTemplateClick = async (templateId: string, isShowForm = true) => {
    // try {
    setIsLoadingTemplate(true);
    // const template = await getEmailTemplateById(parseInt(templateId, 10));
    setEditingTemplateId(parseInt(templateId, 10));
    if (isShowForm) setShowForm(true);
    // } catch (error) {
    //   setEditingTemplateId(parseInt(templateId, 10));
    //   if (isShowForm) setShowForm(true);
    // } finally {
    //   setIsLoadingTemplate(false);
    // }
  };

  const handleFormDiscard = () => {
    setShowForm(false);
    setEditingTemplateId(null);
    setIsFormValid(false);
    setCurrentFormValues(emptyTemplate);
  };

  const handleFormUpdate = async (data: TemplateFormData, isCreate = false) => {
    try {
      setIsSubmitting(true);

      const apiData: CreateEmailTemplateRequest = {
        title: data.templateName,
        subject: data.subject,
        message: data.message,
        timeDelay: data.delay === 'IMMEDIATELY' ? 'IMMEDIATELY' : data.delay,
        fileIds: (data.attachments || []).map((attachment: any) =>
          typeof attachment === 'number'
            ? attachment
            : parseInt(attachment.id || attachment, 10)
        ),
        hasFollowup: data.hasFollowup,
        ...(data.hasFollowup && {
          followupTitle: data.followupTitle,
          followupMessage: data.followupMessage,
          followupPeriod: data.followupPeriod,
          followupFileIds: (data.followupAttachments || []).map(
            (attachment: any) =>
              typeof attachment === 'number'
                ? attachment
                : parseInt(attachment.id || attachment, 10)
          ),
        }),
      };

      if (editingTemplateId && !isCreate) {
        await updateEmailTemplate(editingTemplateId, apiData);
      } else {
        await createEmailTemplate(apiData);
      }

      setShowForm(false);
      setEditingTemplateId(null);
      setIsFormValid(false);
      setCurrentFormValues(emptyTemplate);

      refetch();
    } catch (error) {
      console.error('Error saving template:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const menu = [
    {
      id: 'duplicate',
      icon: 'duplicate',
      title: t('duplicate'),
      onClick: (template) => {
        // handleTemplateClick(template.id, false);
        // handleTemplateClick(template.id, false);
        const newTemple = templates.find(
          (t) => t.id === template.id.toString()
        );
        if (newTemple) {
          const newInitialFormData = {
            templateName: newTemple.title || '',
            subject: newTemple.subject || '',
            message: newTemple.message || '',
            delay: 'IMMEDIATELY',
            attachments: (newTemple.fileIds || []).map(
              (id: string | number) => ({
                id,
              })
            ),
            hasFollowup: newTemple.hasFollowup || false,
            followupPeriod: newTemple?.followupPeriod || '',
            followupTitle: newTemple?.followupTitle || '',
            followupMessage: newTemple?.followupMessage || '',
            followupAttachments: newTemple?.followupAttachments || [],
          };

          handleFormUpdate(newInitialFormData, true);
        }
      },
    },
    {
      id: 'edit',
      icon: 'pen',
      title: t('edit'),
      onClick: (template) => {
        handleTemplateClick(template.id);
      },
    },
    {
      id: 'delete',
      icon: 'trash',
      title: t('delete'),
      onClick: (template) => {
        openConfirmDialog({
          title: t('delete_template'),
          message: t('delete_template_confirm'),
          confirmButtonText: t('delete'),
          cancelButtonText: t('cancel'),
          isAjaxCall: true,
          apiProps: {
            func: () => removeEmailTemplate(template?.id),
          },
        });
      },
    },
  ];

  console.log('initialFormData', initialFormData);

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
      modalClassName="overflow-hidden"
      contentClassName="overflow-hidden"
      modalDialogClassName="overflow-hidden"
    >
      <ModalHeaderSimple
        title={
          showForm
            ? editingTemplateId
              ? t('edit_template')
              : t('create_template')
            : t('auto_reply')
        }
        backButtonProps={{
          onClick: showForm ? handleFormDiscard : handleClose,
        }}
        noCloseButton
        hideBack={false}
      />

      <ModalBody className="p-6 h-full overflow-auto mb-8">
        {showForm ? (
          <TemplateForm
            formData={initialFormData}
            onSubmit={handleFormUpdate}
            isLoading={isLoadingTemplate}
            onChange={handleFormChange}
          />
        ) : (
          <Flex className="flex-col h-full gap-8">
            <Flex className="flex-col gap-20 h-full">
              <SearchInputV2
                placeholder={t('search_templates')}
                onChange={(value) => setSearchQuery(value)}
                value={searchQuery}
              />

              <Flex className="flex-col gap-12 flex-1 overflow-y-auto">
                {isLoading ? (
                  <Flex flexDir="column" className="gap-12 px-0 py-16_20 !pb-0">
                    {[...Array(10).keys()].map((_, index) => (
                      <Skeleton
                        key={index}
                        className="h-[56px] w-full rounded-lg"
                      />
                    ))}
                  </Flex>
                ) : filteredTemplates.length === 0 ? (
                  <Typography>No templates found</Typography>
                ) : (
                  filteredTemplates.map((template) => (
                    <MenuItem
                      key={template.id}
                      title={template.title}
                      titleClassName="text-lg font-semibold"
                      actionClassName="justify-end"
                      subTitle={
                        <Typography className="flex flex-col mt-4 text-xs whitespace-nowrap">
                          <Typography className="!text-colorIconForth2 text-sm font-medium">
                            {t('subject')}:
                          </Typography>
                          <Typography
                            isTruncated
                            className="text-primaryText text-xs font-normal"
                          >
                            {template.subject}
                          </Typography>
                        </Typography>
                      }
                      className=" !rounded !p-12 overflow-hidden flex justify-between items-center border border-solid !border-techGray_20 !bg-gray_5 hover:border-brand hover:bg-brand-50 shrink-0"
                      actionElement={
                        <Flex className="justify-between items-end gap-12 ">
                          <PopperMenu
                            placement="bottom-end"
                            onOpen={onOpen}
                            onClose={onClose}
                            closeOnScroll
                            buttonComponent={
                              <IconButton
                                type="far"
                                name="ellipsis-h"
                                size="md"
                                noHover
                                colorSchema="transparent"
                              />
                            }
                          >
                            {menu?.map((x: any) => (
                              <MenuItem
                                key={x.id}
                                iconName={x.icon}
                                title={x.title}
                                secondaryLabel={x.helper}
                                onClick={() => x.onClick(template)}
                                iconType={x.iconType || 'far'}
                              />
                            ))}
                          </PopperMenu>
                          <Button
                            schema={
                              currentDefaultTemplateId === template.id
                                ? 'success-semi-transparent'
                                : 'secondary-dark'
                            }
                            variant="thin"
                            label={
                              currentDefaultTemplateId === template.id
                                ? t('default')
                                : t('set_as_default')
                            }
                            leftIcon={
                              currentDefaultTemplateId === template.id
                                ? 'check'
                                : undefined
                            }
                            leftType="fas"
                            labelColor={
                              currentDefaultTemplateId === template.id
                                ? 'success'
                                : 'primaryText'
                            }
                            onClick={(e) => {
                              handleSetDefault(
                                template.id,
                                currentDefaultTemplateId === template.id,
                                e
                              );
                            }}
                            disabled={isUpdatingDefault === template.id}
                            isLoading={isUpdatingDefault === template.id}
                          />
                        </Flex>
                      }
                    />
                  ))
                )}
              </Flex>
            </Flex>
          </Flex>
        )}
      </ModalBody>

      <ModalFooter>
        {showForm ? (
          <Flex flexDir="row" className="flex-row gap-4 flex-shrink-0">
            <Button
              label={t('discard')}
              schema="gray"
              variant="default"
              onClick={handleFormDiscard}
              className="flex-1"
            />
            <Button
              label={editingTemplateId ? t('update') : t('create')}
              schema="primary-blue"
              variant="default"
              onClick={() => handleFormUpdate(currentFormValues)}
              className="flex-1"
              disabled={!isFormValid || isSubmitting || isLoadingTemplate}
              isLoading={isSubmitting}
            />
          </Flex>
        ) : (
          <Button
            fullWidth
            label={t('create_template')}
            leftIcon="plus"
            leftType="fas"
            schema="semi-transparent"
            variant="default"
            onClick={handleCreate}
          />
        )}
      </ModalFooter>
    </FixedRightSideModal>
  );
};

export default AutoReplyModal;
