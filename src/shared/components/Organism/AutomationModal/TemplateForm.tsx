import React, { useEffect } from 'react';
import Flex from '@shared/uikit/Flex';
import Form from '@shared/uikit/Form';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import Skeleton from '@shared/uikit/Skeleton';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Tooltip from '@shared/uikit/Tooltip';
import Icon from '@shared/uikit/Icon';

interface TemplateFormData {
  templateName: string;
  subject: string;
  message: string;
  delay: string;
  attachments: any[];
  hasFollowup?: boolean;
  followupPeriod?: string;
  followupTitle?: string;
  followupMessage?: string;
  followupAttachments?: any[];
  default?: boolean;
}

interface TemplateFormProps {
  formData: TemplateFormData;
  onSubmit: (data: TemplateFormData) => void;
  isLoading?: boolean;
  onChange?: (values: TemplateFormData, isValid: boolean) => void;
  omitFollowup?: boolean;
  readOnly?: boolean;
  showAttachments?: boolean;
}

const delayOptions = [
  { label: 'Immediately', value: 'IMMEDIATELY' },
  { label: '10 minutes', value: '10_MINUTES' },
  { label: '30 minutes', value: '30_MINUTES' },
  { label: '2 hours', value: '2_HOURS' },
  { label: '5 hours', value: '5_HOURS' },
  { label: '10 Hours', value: '10_HOURS' },
  { label: '12 Hours', value: '12_HOURS' },
  { label: '24 Hours', value: '24_HOURS' },
  { label: '48 Hours', value: '48_HOURS' },
];

const followupPeriodOptions = [
  { label: '3 days', value: '_3_DAYS' },
  { label: '5 days', value: '_5_DAYS' },
  { label: '7 days', value: '_7_DAYS' },
  { label: '14 days', value: '_14_DAYS' },
];

const TemplateForm: React.FC<TemplateFormProps> = ({
  formData,
  onSubmit,
  isLoading = false,
  onChange,
  omitFollowup,
  readOnly,
  showAttachments,
}) => {
  const { t } = useTranslation();

  const groups = (props: any) => {
    let baseGroups = [
      {
        name: 'delay',
        cp: 'dropdownSelect',
        label: t('delay'),
        options: delayOptions,
        required: true,
        wrapStyle: '!mb-8',
      },
      {
        name: 'templateName',
        cp: 'input',
        label: t('template_name'),
        required: true,
        wrapStyle: '!mb-8',
      },
      {
        name: 'subject',
        cp: 'input',
        label: t('subject'),
        required: true,
        wrapStyle: '!mb-8',
      },
      {
        name: 'message',
        cp: 'richtext',
        label: t('message'),
        required: true,
        variant: 'form-input',
        maxLength: 80000,
        helperText: t('use_brackets_for_dynamic_data'),
        className: '!min-h-[120px] bg-popOverBg_white',
        wrapStyle: '!mb-8',
      },
      {
        name: 'hasFollowup',
        cp: 'checkBox',
        label: t('have_follow_up_message'),
        required: false,
        wrapStyle: '!mb-8',
        visibleOptionalLabel: false,

        labelProps: {
          size: 15,
          font: '500',
          color: 'primaryText',
        },

        rightComponent: (
          <Tooltip
            placement="top"
            trigger={
              <Icon
                color="secondaryDisabledText"
                type="fal"
                name="info-circle"
                size={15}
              />
            }
          >
            {t('send_a_follow-up')}
          </Tooltip>
        ),
      },
    ];
    if (props.showAttachments !== false) {
      baseGroups.splice(4, 0, {
        name: 'attachments',
        cp: 'attachmentPicker',
        label: t('attachments'),
        required: false,
        wrapStyle: '!mb-8',
      });
    }
    if (props.omitFollowup) {
      // Remove delay, hasFollowup, and all follow-up fields
      baseGroups = baseGroups.filter(
        (g) => !['delay', 'hasFollowup'].includes(g.name)
      );
    }
    if (!props.omitFollowup && props.values.hasFollowup) {
      baseGroups = [
        ...baseGroups,
        {
          name: 'followupPeriod',
          cp: 'dropdownSelect',
          label: t('follow_up_after'),
          options: followupPeriodOptions,
          required: false,
          visibleOptionalLabel: false,
          wrapStyle: '!mb-8',
        },
        {
          name: 'followupTitle',
          cp: 'input',
          label: t('message_title'),
          required: false,
          wrapStyle: '!mb-8',
          visibleOptionalLabel: false,
        },
        {
          name: 'followupMessage',
          cp: 'richtext',
          label: t('message'),
          required: false,
          variant: 'form-input',
          maxLength: 80000,
          helperText: t('use_brackets_for_dynamic_data'),
          className: '!min-h-[120px] bg-popOverBg_white',
          wrapStyle: '!mb-8',
          visibleOptionalLabel: false,
        },
        {
          name: 'followupAttachments',
          cp: 'attachmentPicker',
          label: t('attachments'),
          required: false,
          wrapStyle: '!mb-8',
          visibleOptionalLabel: false,
        },
      ];
    }
    if (props.readOnly) {
      baseGroups = baseGroups.map((g) => ({
        ...g,
        readOnly: true,
        disabled: true,
      }));
    }

    return baseGroups;
  };

  return (
    <Flex className="w-full p-20 rounded-xl bg-gray_5">
      {isLoading ? (
        <Flex className="w-full">
          <Skeleton
            style={{
              width: '100%',
              height: 40,
              borderRadius: 4,
              marginBottom: 16,
            }}
          />
        </Flex>
      ) : (
        <Form
          initialValues={formData}
          onSuccess={onSubmit}
          enableReinitialize
          validateOnChange
        >
          {(props) => {
            useEffect(() => {
              onChange?.(props.values, props.isValid);
            }, [props.values, props.isValid]);

            return (
              <Flex className="w-full">
                <DynamicFormBuilder
                  groups={groups({
                    ...props,
                    omitFollowup,
                    readOnly,
                    showAttachments,
                  })}
                />
              </Flex>
            );
          }}
        </Form>
      )}
    </Flex>
  );
};

export default TemplateForm;
